import React from 'react';
import { motion } from 'framer-motion';
import cardBackImage from '../assets/card-back.png';

interface DeckProps {
  cardCount: number;
  isShuffling: boolean;
  onShuffle: () => void;
  canShuffle: boolean;
}

export const Deck: React.FC<DeckProps> = ({ 
  cardCount, 
  isShuffling, 
  onShuffle, 
  canShuffle 
}) => {
  const renderDeckCards = () => {
    const cards = [];
    const maxVisible = Math.min(cardCount, 8); // Show max 8 cards for visual effect
    
    for (let i = 0; i < maxVisible; i++) {
      cards.push(
        <motion.div
          key={i}
          className="deck-card"
          style={{
            zIndex: maxVisible - i,
            transform: `translate(${i * 2}px, ${i * -1}px)`,
          }}
          animate={isShuffling ? {
            x: [0, Math.random() * 20 - 10, 0],
            y: [0, Math.random() * 20 - 10, 0],
            rotate: [0, Math.random() * 10 - 5, 0],
          } : {}}
          transition={{
            duration: 0.5,
            delay: i * 0.1,
            repeat: isShuffling ? Infinity : 0,
            repeatType: "reverse"
          }}
        >
          <img src={cardBackImage} alt="Card back" />
        </motion.div>
      );
    }
    return cards;
  };

  return (
    <div className="deck-container">
      <div className="deck-info">
        <h3>Oracle Deck</h3>
        <p>{cardCount} cards</p>
      </div>
      
      <motion.div 
        className="deck"
        whileHover={canShuffle ? { scale: 1.05 } : {}}
      >
        {renderDeckCards()}
      </motion.div>
      
      {canShuffle && (
        <motion.button
          className="shuffle-button"
          onClick={onShuffle}
          disabled={isShuffling}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {isShuffling ? 'Shuffling...' : 'Shuffle Deck'}
        </motion.button>
      )}
    </div>
  );
};
